/* Main Colors */
:root {
  --primary-color: #4CAF50;
  --secondary-color: #8BC34A;
  --accent-color: #009688;
  --light-bg: #F1F8E9;
  --dark-bg: #1B5E20;
  --text-color: #333;
  --text-light: #6c757d;
  --white: #ffffff;
  --success: #28a745;
  --warning: #ffc107;
  --danger: #dc3545;
  --info: #17a2b8;
  --light-accent: #E8F5E9;
  --medium-accent: #C8E6C9;
}

/* Global Styles */
body {
  font-family: 'Roboto', sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  background-color: var(--white);
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: var(--dark-bg);
}

a {
  color: var(--primary-color);
  transition: all 0.3s ease;
}

a:hover {
  color: var(--accent-color);
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.text-primary {
  color: var(--primary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
  position: relative;
}

.bg-primary::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(to right, var(--accent-color), var(--primary-color));
  opacity: 0.3;
}

.bg-primary.text-white {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.border-primary {
  border-color: var(--primary-color) !important;
}

/* Navbar */
.navbar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0.8rem 1rem;
  background-color: var(--white);
}

.navbar-brand {
  font-weight: 700;
  font-family: 'Montserrat', sans-serif;
  color: var(--primary-color);
}

.navbar-light .navbar-nav .nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin: 0 0.2rem;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link.active {
  color: var(--primary-color);
  background-color: rgba(76, 175, 80, 0.1);
}

.dropdown-menu {
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 0.5rem;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

.dropdown-item:hover, .dropdown-item:focus {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--primary-color);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, rgba(56, 142, 60, 0.95), rgba(0, 121, 107, 0.9)), url('/static/img/green-background.jpg');
  background-size: cover;
  background-position: center;
  color: var(--white);
  padding: 5rem 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M20 20.5V18H0v-2h20v-2H0v-2h20v-2H0V8h20V6H0V4h20V2H0V0h22v20h2V0h2v20h2V0h2v20h2V0h2v20h2V0h2v20h2v2H20v-1.5zM0 20h2v20H0V20zm4 0h2v20H4V20zm4 0h2v20H8V20zm4 0h2v20h-2V20zm4 0h2v20h-2V20zm4 4h20v2H20v-2zm0 4h20v2H20v-2zm0 4h20v2H20v-2zm0 4h20v2H20v-2z" fill="%23ffffff" fill-opacity="0.05" fill-rule="evenodd"/%3E%3C/svg%3E');
  pointer-events: none;
}

.hero-section h1 {
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.2);
  font-weight: 700;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 2;
}

.hero-section p {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15), 0 0 20px rgba(255, 255, 255, 0.1);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
  position: relative;
  z-index: 2;
}

.btn-light {
  background-color: var(--white);
  color: var(--primary-color);
  border-color: var(--white);
  font-weight: 500;
}

.btn-light:hover {
  background-color: var(--light-accent);
  color: var(--dark-bg);
  border-color: var(--light-accent);
}

.btn-outline-light {
  color: var(--white);
  border-color: var(--white);
}

.btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--white);
  border-color: var(--white);
}

/* Hero-specific button styles */
.hero-section .btn-light {
  background-color: rgba(255, 255, 255, 0.95);
  color: var(--dark-bg);
  border-color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.hero-section .btn-light:hover {
  background-color: var(--white);
  color: var(--primary-color);
  border-color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.hero-section .btn-outline-light {
  color: rgba(255, 255, 255, 0.95);
  border-color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.hero-section .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--white);
  border-color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* Cards */
.card {
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.card-header {
  font-weight: 600;
  border-bottom: none;
  background-color: var(--light-accent);
}

.mention-card {
  border-left: 4px solid var(--primary-color);
  background-color: var(--white);
}

.mention-card:hover {
  border-left: 4px solid var(--accent-color);
}

.blog-card .card-body {
  padding: 1.5rem;
}

.blog-card .text-primary i {
  background-color: var(--light-accent);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* Calculator Section */
.calculator-section {
  background-color: var(--light-bg);
  padding: 4rem 0;
}

.form-control {
  border-radius: 6px;
  padding: 0.6rem 1rem;
  border: 1px solid #dee2e6;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-select {
  border-radius: 6px;
  padding: 0.6rem 1rem;
  border: 1px solid #dee2e6;
}

.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

/* Results Section */
#simulation-results {
  transition: all 0.3s ease;
}

.results-summary {
  background-color: var(--light-accent);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-summary h4 {
  color: var(--dark-bg);
  margin-bottom: 1rem;
}

.results-summary .total-score {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-align: center;
  margin: 1rem 0;
}

.chart-container {
  position: relative;
  height: 400px;
  background-color: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table-container {
  background-color: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table-container h5 {
  color: var(--dark-bg);
  font-weight: 600;
}

#results-details {
  max-height: 400px;
  overflow-y: auto;
}

#results-details table {
  margin-bottom: 0;
}

#results-details thead th {
  position: sticky;
  top: 0;
  background-color: var(--light-accent);
  z-index: 1;
}

#results-details tbody tr:hover {
  background-color: var(--light-accent);
}

#results-details tfoot {
  position: sticky;
  bottom: 0;
  background-color: var(--white);
  z-index: 1;
}

/* Testimonials */
.testimonial {
  background-color: var(--white);
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial .rounded-circle {
  background-color: var(--primary-color) !important;
}

/* FAQ Section */
.accordion-item {
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.accordion-button {
  font-weight: 600;
  background-color: var(--white);
}

.accordion-button:not(.collapsed) {
  background-color: var(--light-accent);
  color: var(--primary-color);
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: none;
  border-color: rgba(76, 175, 80, 0.5);
}

/* Footer */
footer {
  background-color: var(--dark-bg);
  color: var(--white);
  padding: 4rem 0 2rem;
  position: relative;
}

footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

footer h5 {
  color: var(--white);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

footer .text-secondary {
  color: rgba(255, 255, 255, 0.8) !important;
}

footer .text-secondary:hover {
  color: var(--white) !important;
}

/* Alternating Sections */
.bg-light {
  background-color: var(--light-bg) !important;
}

.bg-white {
  background-color: var(--white) !important;
}

/* Media Queries */
@media (max-width: 991.98px) {
  .chart-container {
    height: 350px;
    margin-bottom: 1.5rem;
  }
  
  .table-container {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 767.98px) {
  .hero-section {
    padding: 3rem 0;
  }
  
  .calculator-section {
    padding: 3rem 0;
  }
  
  .navbar-collapse {
    background-color: var(--white);
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .results-summary .total-score {
    font-size: 2rem;
  }
  
  #results-details {
    max-height: 300px;
  }
} 